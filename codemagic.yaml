workflows:
  flutter-workflow:
    name: Flutter Workflow
    max_build_duration: 120
    instance_type: mac_mini_m2
    integrations:
      app_store_connect: codemagic_cd
    environment:
      groups:
        - sluqe-mobile-app
      android_signing:
        - keystore_reference
      ios_signing:
        distribution_type: app_store
        bundle_identifier: io.boet.sluqe
      vars:
        PACKAGE_NAME: "io.boet.sluqe"
        GOOGLE_PLAY_TRACK: "internal"
        CM_CLONE_DEPTH: 10
        FIREBASE_DEV_PROJECT: "sluqe-qa"
        FIREBASE_PROD_PROJECT: "sluqe-prod"
        JAVA_TOOL_OPTIONS: "-Xmx4096m"
      flutter: stable
      cocoapods: default
    triggering:
      events:
        - tag
      tag_patterns:
        - pattern: "v*.*.*"
          include: true
    scripts:
      - name: Set up automatic versioning from GitHub tag
        script: |
          echo "📋 GitHub tag: $CM_TAG"
          echo "📋 Build number: $PROJECT_BUILD_NUMBER"

          # Extract version from GitHub tag for build name (e.g., v1.2.3 -> 1.2.3)
          if [[ "$CM_TAG" =~ ^v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
            export BUILD_NAME="${BASH_REMATCH[1]}"
            echo "BUILD_NAME=$BUILD_NAME" >> $CM_ENV
            echo "✅ Extracted version: $BUILD_NAME from tag: $CM_TAG"
          else
            # Fallback to a default version if tag format is unexpected
            export BUILD_NAME="1.0.0"
            echo "BUILD_NAME=$BUILD_NAME" >> $CM_ENV
            echo "⚠️  Unexpected tag format, using fallback version: $BUILD_NAME"
          fi

          # Use Codemagic's automatic build number incrementing
          export BUILD_NUMBER=$PROJECT_BUILD_NUMBER
          echo "BUILD_NUMBER=$BUILD_NUMBER" >> $CM_ENV

          echo "🚀 Final build version: $BUILD_NAME+$BUILD_NUMBER"

          # Determine if this is a production release based on tag pattern
          if [[ "$CM_TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]] && [[ ! "$CM_TAG" =~ (beta|alpha|rc|dev) ]]; then
            export IS_PRODUCTION_RELEASE=true
            echo "IS_PRODUCTION_RELEASE=true" >> $CM_ENV
            echo "🚀 Production release detected: $CM_TAG"
          else
            export IS_PRODUCTION_RELEASE=false
            echo "IS_PRODUCTION_RELEASE=false" >> $CM_ENV
            echo "🧪 Development/Testing release detected: $CM_TAG"
          fi

      - name: Generate release notes from GitHub release
        script: |
          if command -v curl &> /dev/null; then
            GITHUB_RELEASE_URL="https://api.github.com/repos/${CM_REPO_SLUG}/releases/tags/${CM_TAG}"
            echo "Fetching release notes from: $GITHUB_RELEASE_URL"
            RELEASE_BODY=$(curl -s -H "Accept: application/vnd.github.v3+json" "$GITHUB_RELEASE_URL" | grep -o '"body":"[^"]*"' | sed 's/"body":"//;s/"$//' | sed 's/\\n/\n/g' | sed 's/\\r//g')
            if [ -n "$RELEASE_BODY" ] && [ "$RELEASE_BODY" != "null" ]; then
              echo "Using GitHub release notes"
              echo "$RELEASE_BODY" > release_notes.txt
            else
              echo "No GitHub release found, generating from git commits"
              git fetch --all --tags
              prev_tag=$(git for-each-ref --sort=-creatordate --format '%(refname:short)' refs/tags | grep -v "^$CM_TAG$" | head -n 1)
              if [ -n "$prev_tag" ]; then
                notes=$(git log --pretty=format:"- %s" "$prev_tag"..HEAD)
                echo -e "Changes since $prev_tag:\n$notes" > release_notes.txt
              else
                echo "Release $CM_TAG" > release_notes.txt
              fi
            fi
          else
            echo "curl not available, using tag-based release notes"
            echo "Release $CM_TAG" > release_notes.txt
          fi
          echo "Generated release notes:"
          cat release_notes.txt

      - name: Set up local.properties
        script: |
          echo "flutter.sdk=$HOME/programs/flutter" > "$CM_BUILD_DIR/android/local.properties"

      - name: Get Flutter packages
        script: |
          flutter packages pub get

      - name: Flutter analyze
        script: |
          flutter analyze || true

      - name: Flutter unit tests
        script: |
          flutter test || true
        ignore_failure: true

      - name: Install FlutterFire CLI and Authenticate
        script: |
          echo "Installing FlutterFire CLI..."
          dart pub global activate flutterfire_cli
          echo "Authenticating with Firebase..."
          echo "FlutterFire CLI installed and authenticated successfully"

      - name: Setup Firebase Environment
        script: |
          echo "Setting up Firebase configuration..."
          cp android/app/google-services.json android/app/google-services-backup.json || echo "No existing Android config to backup"
          cp ios/Runner/GoogleService-Info.plist ios/Runner/GoogleService-Info-backup.plist || echo "No existing iOS config to backup"
          if [ "$IS_PRODUCTION_RELEASE" = "true" ]; then
            echo "🚀 Configuring PRODUCTION Firebase project: $FIREBASE_PROD_PROJECT"
            flutterfire configure --project=$FIREBASE_PROD_PROJECT --platforms=android,ios --yes --token=$FIREBASE_TOKEN
          else
            echo "🧪 Configuring QA Firebase project: $FIREBASE_DEV_PROJECT"
            flutterfire configure --project=$FIREBASE_DEV_PROJECT --platforms=android,ios --yes --token=$FIREBASE_TOKEN
          fi
          echo "Firebase configuration applied successfully"

      - name: Build APK with Flutter
        script: |
          flutter build apk --release --build-name=$BUILD_NAME --build-number=$BUILD_NUMBER

      - name: Build AAB with Flutter
        script: |
          flutter build appbundle --release --build-name=$BUILD_NAME --build-number=$BUILD_NUMBER

      - name: Build iOS
        script: |
          flutter build ios --release --no-codesign --build-name=$BUILD_NAME --build-number=$BUILD_NUMBER
          xcode-project use-profiles
          xcode-project build-ipa \
            --workspace ios/Runner.xcworkspace \
            --scheme Runner

    artifacts:
      - build/**/outputs/**/*.apk
      - build/**/outputs/**/*.aab
      - build/**/outputs/**/mapping.txt
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - flutter_drive.log

    publishing:
      slack:
        channel: "#builds"
        notify_on_build_start: true
        notify:
          success: true
          failure: true

      # google_play:
      #   credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
      #   track: $GOOGLE_PLAY_TRACK
      #   submit_as_draft: false
      #   changes_not_sent_for_review: true

      app_store_connect:
        auth: integration
        submit_to_testflight: true
        submit_to_app_store: false
