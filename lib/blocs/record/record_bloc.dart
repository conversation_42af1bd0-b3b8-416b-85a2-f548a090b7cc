import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/models/recording_progress.dart';
import 'package:sluqe/services/record/background_upload_service.dart';
import 'package:sluqe/services/record/foreground_service.dart';
import 'package:sluqe/services/record/ios_live_activity_service.dart';
import 'package:sluqe/services/record/recording_service.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:sluqe/services/record/storage_service.dart';
import 'package:sluqe/services/record/upload_service.dart';
import 'package:sluqe/services/upload_progress_service.dart';

part 'record_event.dart';
part 'record_state.dart';

class RecordBloc extends Bloc<RecordEvent, RecordState> {
  final RecordingService _recordingService;
  final StorageService _storageService;
  final UploadService _uploadService;
  final BackgroundUploadService _backgroundUploadService;
  final UploadProgressService _progressService;
  String? _userId;
  Timer? _timer;
  StreamSubscription? _foregroundTaskSubscription;
  Folder? selectedFolder;

  RecordBloc({
    RecordingService? recordingService,
    StorageService? storageService,
    UploadService? uploadService,
    BackgroundUploadService? backgroundUploadService,
  }) : _recordingService = recordingService ?? RecordingService(),
       _storageService = storageService ?? StorageService(),
       _uploadService = uploadService ?? UploadService(),
       _backgroundUploadService =
           backgroundUploadService ?? BackgroundUploadService(),
       _progressService = UploadProgressService(),
       super(RecordState()) {
    on<InitializeRecording>(_onInitialize);
    on<StartRecording>(_onStartRecording);
    on<StopRecording>(_onStopRecording);
    on<ResumeRecording>(_onResumeRecording);
    on<DiscardRecording>(_onDiscardRecording);
    on<UpdateDuration>(_onUpdateDuration);
    // on<UpdateFolder>(_onUpdateFolder);
    on<SaveProgress>(_onSaveProgress);
    on<ForegroundTaskStopped>(_onForegroundTaskStopped);
    on<UploadCompleted>(_onUploadCompleted);
    on<UploadFailed>(_onUploadFailed);
    on<UploadProgress>(_onUploadProgress);
    on<UploadStarted>(_onUploadStarted);
    on<RetryFailedUploads>(_onRetryFailedUploads);
  }
  RecorderController get recorderController => _recordingService.controller;

  Future<void> _onInitialize(
    InitializeRecording event,
    Emitter<RecordState> emit,
  ) async {
    try {
      _recordingService.initialize();
      _userId = event.userId;

      FlutterForegroundTask.addTaskDataCallback(_onReceiveTaskData);

      final progress = await _storageService.getRecordingProgress();
      if (progress != null) {
        selectedFolder = progress.selectedFolder;
        emit(
          state.copyWith(
            recordingPath: progress.path,
            recordingDuration: progress.duration,
            hasRecoveredRecording: true,
            isInitialized: true,
            selectedFolder: progress.selectedFolder?.path ?? "Select Folder",
          ),
        );
      } else {
        emit(state.copyWith(isInitialized: true));
      }

      await _backgroundUploadService.resumeProcessing();

      final pendingUploads = await _storageService.getPendingUploads();
      if (pendingUploads.isNotEmpty) {
        Future.delayed(Duration(seconds: 2), () {
          add(RetryFailedUploads());
        });
      } else {
        add(RetryFailedUploads());
      }
    } catch (e) {
      emit(state.copyWith(isInitialized: true));
    }
  }

  Future<void> _onStartRecording(
    StartRecording event,
    Emitter<RecordState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        'recording_start_time',
        DateTime.now().millisecondsSinceEpoch,
      );

      await ForegroundService.initialize();
      await ForegroundService.start(
        formatDuration(0),
        onStopPressed: () {
          add(StopRecording());
        },
      );
      ForegroundService.addTaskDataCallback(_onReceiveTaskData);
      final path = await _recordingService.startRecording();
      selectedFolder = event.folder;
      emit(
        state.copyWith(
          isRecording: true,
          isRecordingCompleted: false,
          recordingPath: path,
          recordingDuration: 0,
          hasRecoveredRecording: false,
        ),
      );
      _startTimer();
    } catch (e) {
      print('Error starting recording: $e');
    }
  }

  Future<void> _onStopRecording(
    StopRecording event,
    Emitter<RecordState> emit,
  ) async {
    try {
      await ForegroundService.stop();
      final path = await _recordingService.stopRecording();
      _timer?.cancel();
      await _storageService.clearRecordingProgress();
      emit(
        state.copyWith(
          isRecording: false,
          isRecordingCompleted: true,
          recordingPath: path,
        ),
      );

      if (path != null) {
        _uploadRecording(path);
      }
    } catch (e) {
      print('Error stopping recording: $e');
    }
  }

  Future<void> _onResumeRecording(
    ResumeRecording event,
    Emitter<RecordState> emit,
  ) async {
    try {
      if (state.recordingPath == null) {
        print('ERROR: No recording path to resume!');
        return;
      }

      // Calculate adjusted start time for resumed recording
      final prefs = await SharedPreferences.getInstance();
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final adjustedStartTime = currentTime - (state.recordingDuration * 1000);
      await prefs.setInt('recording_start_time', adjustedStartTime);

      emit(
        state.copyWith(
          isRecording: true,
          isRecordingCompleted: false,
          hasRecoveredRecording: false,
        ),
      );

      await ForegroundService.initialize();
      await ForegroundService.start(
        formatDuration(state.recordingDuration),
        onStopPressed: () {
          add(StopRecording());
        },
      );
      ForegroundService.addTaskDataCallback(_onReceiveTaskData);

      final resumedPath = await _recordingService.resumeRecording(
        state.recordingPath!,
      );
      print('Resumed recording path: $resumedPath');

      if (resumedPath != state.recordingPath) {
        emit(state.copyWith(recordingPath: resumedPath));
      }

      add(SaveProgress());
      _startTimer();
    } catch (e) {
      print('Error resuming recording: $e');
    }
  }

  Future<void> _onDiscardRecording(
    DiscardRecording event,
    Emitter<RecordState> emit,
  ) async {
    try {
      await _storageService.clearRecordingProgress();
      emit(
        state.copyWith(
          recordingPath: null,
          recordingDuration: 0,
          isRecording: false,
          isRecordingCompleted: false,
          hasRecoveredRecording: false,
        ),
      );
    } catch (e) {
      print('Error discarding recording: $e');
    }
  }

  void _onUpdateDuration(UpdateDuration event, Emitter<RecordState> emit) {
    emit(state.copyWith(recordingDuration: event.duration));
  }

  // void _onUpdateFolder(UpdateFolder event, Emitter<RecordState> emit) {
  //   emit(state.copyWith(selectedFolder: event.folder));
  // }

  Future<void> _onSaveProgress(
    SaveProgress event,
    Emitter<RecordState> emit,
  ) async {
    try {
      if (state.recordingPath != null) {
        final fileName = path.basename(state.recordingPath!);

        final progress = RecordingProgress(
          path: state.recordingPath!,
          fileName: fileName,
          duration: state.recordingDuration,
          lastSaveTime: DateTime.now().millisecondsSinceEpoch,
          isActive: state.isRecording,
          selectedFolder: selectedFolder,
        );
        await _storageService.saveRecordingProgress(progress);
      }
    } catch (e) {
      print('Error saving progress: $e');
    }
  }

  void _onForegroundTaskStopped(
    ForegroundTaskStopped event,
    Emitter<RecordState> emit,
  ) {
    add(StopRecording());
  }

  void _onUploadCompleted(UploadCompleted event, Emitter<RecordState> emit) {
    _progressService.setUploading(false);
    _progressService.updateProgress(1.0);
    emit(
      state.copyWith(
        isUploadFailed: false,
        isUploadSuccess: true,
        isUploading: false,
        uploadProgress: 1.0,
      ),
    );
  }

  void _onUploadFailed(UploadFailed event, Emitter<RecordState> emit) {
    _progressService.setUploading(false);
    emit(
      state.copyWith(
        isUploadFailed: true,
        isUploading: false,
        uploadError: event.error,
      ),
    );
  }

  void _onUploadProgress(UploadProgress event, Emitter<RecordState> emit) {
    _progressService.updateProgress(event.progress);
    emit(state.copyWith(uploadProgress: event.progress));
  }

  void _onUploadStarted(UploadStarted event, Emitter<RecordState> emit) {
    _progressService.setUploading(true);
    _progressService.updateProgress(0.0);
    emit(
      state.copyWith(
        isUploading: true,
        uploadProgress: 0.0,
        isUploadFailed: false,
        isUploadSuccess: false,
        uploadError: null,
      ),
    );
  }

  Future<void> _onRetryFailedUploads(
    RetryFailedUploads event,
    Emitter<RecordState> emit,
  ) async {
    final failedUploads = await _storageService.getFailedUploads();
    for (final uploadPath in failedUploads) {
      try {
        await _uploadRecording(uploadPath);
        await _storageService.removeFailedUpload(uploadPath);
      } catch (e) {
        continue;
      }
    }

    final pendingUploads = await _storageService.getPendingUploads();
    for (final uploadData in pendingUploads) {
      try {
        final filePath = uploadData['filePath'] as String;
        final userId = uploadData['userId'] as String;
        final folderMap = uploadData['folder'] as Map<String, dynamic>?;
        final folder = folderMap != null ? Folder.fromMap(folderMap) : null;

        if (await File(filePath).exists()) {
          if (!isClosed) {
            add(UploadStarted());
          }

          await _uploadService.uploadRecording(
            filePath,
            userId,
            folder,
            onProgress: (progress) {
              if (!isClosed) {
                add(UploadProgress(progress));
              }
            },
          );

          await _storageService.markUploadCompleted(filePath);
          if (!isClosed) {
            add(UploadCompleted());
          }
        } else {
          await _storageService.removePendingUpload(filePath);
        }
      } catch (e) {
        continue;
      }
    }
  }

  String formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _onReceiveTaskData(Object data) {
    if (data is Map<String, dynamic>) {
      final action = data['action'];
      if (action == 'stop_recording') {
        add(ForegroundTaskStopped());
      }
    }
  }

  Future<void> _uploadRecording(String path) async {
    try {
      await _storageService.addPendingUpload(
        filePath: path,
        userId: _userId!,
        folder: selectedFolder,
      );

      if (!isClosed) {
        add(UploadStarted());
      }

      await _uploadService.uploadRecording(
        path,
        _userId!,
        selectedFolder,
        onProgress: (progress) {
          if (!isClosed) {
            add(UploadProgress(progress));
          }
        },
      );

      await _storageService.markUploadCompleted(path);
      await _storageService.clearRecordingProgress();
      if (!isClosed) {
        add(UploadCompleted());
      }
    } catch (e) {
      await _backgroundUploadService.addToQueue(path, _userId!, selectedFolder);
      if (!isClosed) {
        add(UploadFailed(e.toString()));
      }
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) async {
      add(UpdateDuration(state.recordingDuration + 1));

      if (state.recordingDuration % 5 == 0) {
        add(SaveProgress());
        ForegroundService.sendDataToTask({
          'action': 'save_progress',
          'duration': state.recordingDuration,
          'path': state.recordingPath,
        });
      }

      if (state.recordingDuration % 5 == 0) {
        await IOSLiveActivityService.updateRecordingActivity(
          formatDuration(state.recordingDuration),
        );
      }
    });
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    _foregroundTaskSubscription?.cancel();
    _recordingService.dispose();
    _backgroundUploadService.dispose();
    ForegroundService.removeTaskDataCallback(_onReceiveTaskData);
    return super.close();
  }
}
